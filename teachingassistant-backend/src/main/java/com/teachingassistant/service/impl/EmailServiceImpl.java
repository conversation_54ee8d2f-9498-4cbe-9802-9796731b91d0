package com.teachingassistant.service.impl;

import com.teachingassistant.service.EmailService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.security.SecureRandom;
import java.util.concurrent.TimeUnit;

/**
 * 邮件服务实现类
 * 
 * <AUTHOR> Assistant System
 */
@Slf4j
@Service
public class EmailServiceImpl implements EmailService {

    @Autowired(required = false)
    private JavaMailSender mailSender;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Value("${spring.mail.username:}")
    private String fromEmail;

    private static final String VERIFICATION_CODE_PREFIX = "email:verification:";
    private static final int CODE_EXPIRE_MINUTES = 5; // 验证码5分钟过期
    private static final int CODE_LENGTH = 6;
    
    @Override
    public void sendVerificationCode(String email, String code) {
        // 检查邮件服务是否可用
        if (mailSender == null || !isEmailConfigured()) {
            log.warn("邮件服务未正确配置，模拟发送验证码: {} -> {}", email, code);
            log.info("验证码: {} (开发环境下直接显示)", code);
            return;
        }

        try {
            SimpleMailMessage message = new SimpleMailMessage();
            if (StringUtils.hasText(fromEmail) && !fromEmail.equals("<EMAIL>")) {
                message.setFrom(fromEmail);
            }
            message.setTo(email);
            message.setSubject("助教排课系统 - 邮箱验证码");
            message.setText(buildVerificationEmailContent(code));

            mailSender.send(message);
            log.info("验证码邮件发送成功: {}", email);

        } catch (Exception e) {
            log.error("验证码邮件发送失败: {}", email, e);
            // 在开发环境下，邮件发送失败时显示验证码
            log.warn("邮件发送失败，开发环境下显示验证码: {}", code);
            // 不抛出异常，允许功能继续使用
        }
    }

    /**
     * 检查邮件配置是否正确
     */
    private boolean isEmailConfigured() {
        return StringUtils.hasText(fromEmail) &&
               !fromEmail.equals("<EMAIL>") &&
               !fromEmail.equals("<EMAIL>");
    }
    
    @Override
    public String generateVerificationCode() {
        SecureRandom random = new SecureRandom();
        StringBuilder code = new StringBuilder();
        
        for (int i = 0; i < CODE_LENGTH; i++) {
            code.append(random.nextInt(10));
        }
        
        return code.toString();
    }
    
    @Override
    public void storeVerificationCode(String email, String code) {
        String key = VERIFICATION_CODE_PREFIX + email;
        redisTemplate.opsForValue().set(key, code, CODE_EXPIRE_MINUTES, TimeUnit.MINUTES);
        log.debug("验证码已存储: {} -> {}", email, code);
    }
    
    @Override
    public boolean verifyCode(String email, String code) {
        if (!StringUtils.hasText(email) || !StringUtils.hasText(code)) {
            return false;
        }

        // 开发环境下的万能验证码
        if ("123456".equals(code) && !isEmailConfigured()) {
            log.info("开发环境使用万能验证码: {}", email);
            return true;
        }

        String key = VERIFICATION_CODE_PREFIX + email;
        String storedCode = redisTemplate.opsForValue().get(key);

        boolean isValid = code.equals(storedCode);
        log.debug("验证码验证结果: {} -> {}", email, isValid);

        return isValid;
    }
    
    @Override
    public void removeVerificationCode(String email) {
        String key = VERIFICATION_CODE_PREFIX + email;
        redisTemplate.delete(key);
        log.debug("验证码已删除: {}", email);
    }
    
    /**
     * 构建验证码邮件内容
     */
    private String buildVerificationEmailContent(String code) {
        return String.format(
            "您好！\n\n" +
            "您正在进行邮箱验证，验证码为：%s\n\n" +
            "验证码有效期为%d分钟，请及时使用。\n" +
            "如果这不是您的操作，请忽略此邮件。\n\n" +
            "助教排课系统\n" +
            "%s",
            code,
            CODE_EXPIRE_MINUTES,
            java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
        );
    }
}
