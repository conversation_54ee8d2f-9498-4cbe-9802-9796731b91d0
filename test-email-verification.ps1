# 邮箱验证码功能测试脚本
$baseUrl = "http://localhost:8080/api"

Write-Host "=== 助教排课系统 - 邮箱验证码功能测试 ===" -ForegroundColor Yellow

# 1. 测试登录
Write-Host "`n1. 测试登录..." -ForegroundColor Cyan
$loginData = @{
    username = "admin"
    password = "admin123"
} | ConvertTo-Json

try {
    $loginResponse = Invoke-RestMethod -Uri "$baseUrl/login" -Method POST -Body $loginData -ContentType "application/json"
    $token = $loginResponse.data.accessToken
    Write-Host "✓ 登录成功，Token: $($token.Substring(0, 20))..." -ForegroundColor Green
} catch {
    Write-Host "✗ 登录失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 2. 测试获取用户信息
Write-Host "`n2. 测试获取用户信息..." -ForegroundColor Cyan
$headers = @{
    "Authorization" = "Bearer $token"
    "Content-Type" = "application/json"
}

try {
    $userInfo = Invoke-RestMethod -Uri "$baseUrl/user/profile" -Method GET -Headers $headers
    Write-Host "✓ 获取用户信息成功" -ForegroundColor Green
    Write-Host "  用户ID: $($userInfo.data.userId)" -ForegroundColor White
    Write-Host "  用户名: $($userInfo.data.username)" -ForegroundColor White
    Write-Host "  真实姓名: $($userInfo.data.realName)" -ForegroundColor White
    Write-Host "  当前邮箱: $($userInfo.data.email)" -ForegroundColor White
} catch {
    Write-Host "✗ 获取用户信息失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 3. 测试发送邮箱验证码
Write-Host "`n3. 测试发送邮箱验证码..." -ForegroundColor Cyan
$emailCodeData = @{
    email = "<EMAIL>"
} | ConvertTo-Json

try {
    $codeResponse = Invoke-RestMethod -Uri "$baseUrl/user/email/send-code" -Method POST -Body $emailCodeData -Headers $headers
    Write-Host "✓ 验证码发送成功: $($codeResponse.message)" -ForegroundColor Green
} catch {
    Write-Host "✗ 验证码发送失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 4. 测试更换邮箱（使用模拟验证码）
Write-Host "`n4. 测试更换邮箱..." -ForegroundColor Cyan
$emailData = @{
    email = "<EMAIL>"
    verificationCode = "123456"
} | ConvertTo-Json

try {
    $emailResponse = Invoke-RestMethod -Uri "$baseUrl/user/email" -Method PUT -Body $emailData -Headers $headers
    Write-Host "✓ 更换邮箱成功: $($emailResponse.message)" -ForegroundColor Green
} catch {
    Write-Host "✗ 更换邮箱失败: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "  这是预期的，因为验证码是模拟的" -ForegroundColor Yellow
}

# 5. 测试更新用户信息（仅姓名）
Write-Host "`n5. 测试更新用户信息..." -ForegroundColor Cyan
$userInfoData = @{
    realName = "系统管理员(测试更新)"
} | ConvertTo-Json

try {
    $updateResponse = Invoke-RestMethod -Uri "$baseUrl/user/info" -Method PUT -Body $userInfoData -Headers $headers
    Write-Host "✓ 更新用户信息成功: $($updateResponse.message)" -ForegroundColor Green
} catch {
    Write-Host "✗ 更新用户信息失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 6. 验证更新后的用户信息
Write-Host "`n6. 验证更新后的用户信息..." -ForegroundColor Cyan
try {
    $updatedUserInfo = Invoke-RestMethod -Uri "$baseUrl/user/profile" -Method GET -Headers $headers
    Write-Host "✓ 获取更新后用户信息成功" -ForegroundColor Green
    Write-Host "  真实姓名: $($updatedUserInfo.data.realName)" -ForegroundColor White
    Write-Host "  邮箱: $($updatedUserInfo.data.email)" -ForegroundColor White
} catch {
    Write-Host "✗ 获取更新后用户信息失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 7. 测试错误情况
Write-Host "`n7. 测试错误情况..." -ForegroundColor Cyan

# 测试邮箱格式错误
Write-Host "  7.1 测试邮箱格式错误..." -ForegroundColor White
$invalidEmailData = @{
    email = "invalid-email"
} | ConvertTo-Json

try {
    $invalidResponse = Invoke-RestMethod -Uri "$baseUrl/user/email/send-code" -Method POST -Body $invalidEmailData -Headers $headers
    Write-Host "✗ 邮箱格式错误测试失败（应该返回错误）" -ForegroundColor Red
} catch {
    Write-Host "✓ 邮箱格式错误测试通过" -ForegroundColor Green
}

# 测试验证码格式错误
Write-Host "  7.2 测试验证码格式错误..." -ForegroundColor White
$invalidCodeData = @{
    email = "<EMAIL>"
    verificationCode = "abc"
} | ConvertTo-Json

try {
    $invalidCodeResponse = Invoke-RestMethod -Uri "$baseUrl/user/email" -Method PUT -Body $invalidCodeData -Headers $headers
    Write-Host "✗ 验证码格式错误测试失败（应该返回错误）" -ForegroundColor Red
} catch {
    Write-Host "✓ 验证码格式错误测试通过" -ForegroundColor Green
}

Write-Host "`n=== 测试完成 ===" -ForegroundColor Yellow
Write-Host "注意：邮件发送功能需要配置真实的SMTP服务器才能正常工作" -ForegroundColor Yellow
